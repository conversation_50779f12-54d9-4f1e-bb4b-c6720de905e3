<?php
/**
 * Create Sample Data for Testing Photo Display
 */

require_once 'app/config/config.php';

echo "<h1>Create Sample Data</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Create upload directory if not exists
$uploadDir = 'public/uploads';
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0755, true)) {
        echo "<p style='color: green;'>✓ Created upload directory: $uploadDir</p>";
        
        // Create .htaccess for uploads
        $htaccess = "Options -Indexes\n";
        $htaccess .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
        $htaccess .= "    Order Allow,Deny\n";
        $htaccess .= "    Allow from all\n";
        $htaccess .= "</FilesMatch>";
        
        file_put_contents($uploadDir . '/.htaccess', $htaccess);
        echo "<p style='color: green;'>✓ Created .htaccess for uploads</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create upload directory</p>";
    }
} else {
    echo "<p style='color: green;'>✓ Upload directory exists</p>";
}

// Handle create sample data
if (isset($_GET['create'])) {
    echo "<h2>Creating Sample Data...</h2>";
    
    // 1. Create sample user if not exists
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch();
    
    if (!$user) {
        $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Sample User', '<EMAIL>', $hashedPassword, 'user']);
        $userId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Created sample user (email: <EMAIL>, password: user123)</p>";
    } else {
        $userId = $user['id'];
        echo "<p style='color: blue;'>ℹ Using existing sample user</p>";
    }
    
    // 2. Get or create category
    $stmt = $pdo->query("SELECT id FROM categories LIMIT 1");
    $category = $stmt->fetch();
    
    if (!$category) {
        $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        $stmt->execute(['Infrastruktur', 'Masalah infrastruktur dan fasilitas umum']);
        $categoryId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Created sample category</p>";
    } else {
        $categoryId = $category['id'];
    }
    
    // 3. Create sample photos
    $samplePhotos = [];
    
    // Create simple colored rectangles as sample images
    for ($i = 1; $i <= 3; $i++) {
        $photoName = 'sample_photo_' . $i . '_' . time() . '.jpg';
        $photoPath = $uploadDir . '/' . $photoName;
        
        // Create a simple JPEG image (1x1 pixel, different colors)
        $colors = [
            1 => "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0C\x14\r\x0C\x0B\x0B\x0C\x19\x12\x13\x0F\x14\x1D\x1A\x1F\x1E\x1D\x1A\x1C\x1C $.' \",#\x1C\x1C(7),01444\x1F'9=82<.342\xFF\xC0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xFF\xC4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xFF\xC4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xDA\x00\x0C\x03\x01\x00\x02\x11\x03\x11\x00\x3F\x00\xAA\xFF\xD9", // Red
            2 => "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0C\x14\r\x0C\x0B\x0B\x0C\x19\x12\x13\x0F\x14\x1D\x1A\x1F\x1E\x1D\x1A\x1C\x1C $.' \",#\x1C\x1C(7),01444\x1F'9=82<.342\xFF\xC0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xFF\xC4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xFF\xC4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xDA\x00\x0C\x03\x01\x00\x02\x11\x03\x11\x00\x3F\x00\x55\xFF\xD9", // Green
            3 => "\xFF\xD8\xFF\xE0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xFF\xDB\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0C\x14\r\x0C\x0B\x0B\x0C\x19\x12\x13\x0F\x14\x1D\x1A\x1F\x1E\x1D\x1A\x1C\x1C $.' \",#\x1C\x1C(7),01444\x1F'9=82<.342\xFF\xC0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xFF\xC4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xFF\xC4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xFF\xDA\x00\x0C\x03\x01\x00\x02\x11\x03\x11\x00\x3F\x00\xFF\xFF\xD9"  // Blue
        ];
        
        file_put_contents($photoPath, $colors[$i]);
        $samplePhotos[] = $photoName;
        echo "<p style='color: green;'>✓ Created sample photo: $photoName</p>";
    }
    
    // 4. Create sample complaints
    $complaints = [
        [
            'title' => 'Jalan Rusak di Depan Sekolah',
            'description' => 'Jalan di depan SD Negeri 1 mengalami kerusakan parah dengan banyak lubang. Hal ini membahayakan keselamatan anak-anak sekolah dan pengguna jalan lainnya.',
            'location' => 'Jl. Pendidikan No. 123, Kelurahan Sukamaju',
            'photo' => $samplePhotos[0],
            'status' => 'pending'
        ],
        [
            'title' => 'Lampu Jalan Mati Total',
            'description' => 'Lampu penerangan jalan di sepanjang Jl. Merdeka sudah mati selama 2 minggu. Kondisi ini sangat membahayakan pengendara di malam hari.',
            'location' => 'Jl. Merdeka Raya, RT 05/RW 02',
            'photo' => $samplePhotos[1],
            'status' => 'process'
        ],
        [
            'title' => 'Saluran Air Tersumbat',
            'description' => 'Saluran air di perumahan mengalami penyumbatan sehingga sering terjadi banjir saat hujan. Perlu segera dibersihkan.',
            'location' => 'Perumahan Griya Asri Blok C',
            'photo' => $samplePhotos[2],
            'status' => 'completed'
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO complaints (user_id, category_id, title, description, location, photo, status, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    foreach ($complaints as $complaint) {
        $result = $stmt->execute([
            $userId,
            $categoryId,
            $complaint['title'],
            $complaint['description'],
            $complaint['location'],
            $complaint['photo'],
            $complaint['status']
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Created complaint: " . $complaint['title'] . "</p>";
        }
    }
    
    echo "<h3>Sample Data Created Successfully!</h3>";
    echo "<p><a href='test-photo-display.php'>View Photo Display Test</a></p>";
    echo "<p><a href='" . BASE_URL . "index.php?url=adminauth/pin'>Login as Admin to Test</a></p>";
}

// Show current status
echo "<h2>Current Status</h2>";

// Check complaints
$stmt = $pdo->query("SELECT COUNT(*) as total FROM complaints WHERE photo IS NOT NULL AND photo != ''");
$photoCount = $stmt->fetchColumn();

echo "<p><strong>Complaints with photos:</strong> $photoCount</p>";

// Check upload directory
$uploadFiles = is_dir($uploadDir) ? count(glob($uploadDir . '/*')) : 0;
echo "<p><strong>Files in upload directory:</strong> $uploadFiles</p>";

if ($photoCount == 0) {
    echo "<h2>Create Sample Data</h2>";
    echo "<p>No complaints with photos found. Create sample data for testing:</p>";
    echo "<p><a href='?create=1' class='btn'>Create Sample Data</a></p>";
} else {
    echo "<h2>Test Photo Display</h2>";
    echo "<p>Sample data exists. Test the photo display:</p>";
    echo "<ul>";
    echo "<li><a href='test-photo-display.php'>View Photo Display Test</a></li>";
    echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin'>Login as Admin</a></li>";
    echo "<li>Navigate to 'Kelola Pengaduan' to see photos</li>";
    echo "</ul>";
}

echo "<h2>Admin Login Info</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>Admin PIN:</strong> 2024</p>";
echo "<p><strong>Admin Email:</strong> <EMAIL></p>";
echo "<p><strong>Admin Password:</strong> admin123</p>";
echo "<p><strong>Sample User Email:</strong> <EMAIL></p>";
echo "<p><strong>Sample User Password:</strong> user123</p>";
echo "</div>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
.btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; }
.btn:hover { background: #0056b3; color: white; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
