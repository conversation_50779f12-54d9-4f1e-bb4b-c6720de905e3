<?php
/**
 * Test Role Values in Database
 */

require_once 'app/config/config.php';

echo "<h1>Test Role Values</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connected</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>");
}

// Check role column
echo "<h2>Role Column Analysis</h2>";
$stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
$roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);

if ($roleColumn) {
    echo "<p><strong>Type:</strong> " . $roleColumn['Type'] . "</p>";
    echo "<p><strong>Null:</strong> " . $roleColumn['Null'] . "</p>";
    echo "<p><strong>Default:</strong> " . ($roleColumn['Default'] ?? 'NULL') . "</p>";
    
    if (strpos($roleColumn['Type'], 'enum') !== false) {
        preg_match_all("/'([^']+)'/", $roleColumn['Type'], $matches);
        $enumValues = $matches[1];
        
        echo "<h3>Available ENUM Values:</h3>";
        echo "<ul>";
        foreach ($enumValues as $value) {
            echo "<li><code>$value</code></li>";
        }
        echo "</ul>";
        
        // Test each value
        echo "<h3>Testing Each Role Value:</h3>";
        foreach ($enumValues as $testRole) {
            try {
                $testEmail = "test_" . $testRole . "@example.com";
                
                // Try to insert
                $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
                $result = $stmt->execute([
                    "Test $testRole User",
                    $testEmail,
                    password_hash('test123', PASSWORD_DEFAULT),
                    $testRole
                ]);
                
                if ($result) {
                    echo "<p style='color: green;'>✓ Role '$testRole' works</p>";
                    
                    // Clean up test user
                    $pdo->prepare("DELETE FROM users WHERE email = ?")->execute([$testEmail]);
                } else {
                    echo "<p style='color: red;'>✗ Role '$testRole' failed</p>";
                }
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Role '$testRole' error: " . $e->getMessage() . "</p>";
            }
        }
        
        // Recommend which role to use
        echo "<h3>Recommendation:</h3>";
        if (in_array('user', $enumValues)) {
            echo "<p style='color: green;'>✓ Use role: <strong>'user'</strong></p>";
            $recommendedRole = 'user';
        } elseif (in_array('warga', $enumValues)) {
            echo "<p style='color: blue;'>ℹ Use role: <strong>'warga'</strong> (Indonesian for citizen)</p>";
            $recommendedRole = 'warga';
        } elseif (in_array('member', $enumValues)) {
            echo "<p style='color: blue;'>ℹ Use role: <strong>'member'</strong></p>";
            $recommendedRole = 'member';
        } else {
            echo "<p style='color: orange;'>⚠ Use role: <strong>'" . $enumValues[0] . "'</strong> (first available)</p>";
            $recommendedRole = $enumValues[0];
        }
        
    } else {
        echo "<p style='color: green;'>✓ Role column is flexible (VARCHAR/TEXT)</p>";
        $recommendedRole = 'user';
    }
} else {
    echo "<p style='color: red;'>✗ Role column not found</p>";
    exit;
}

// Test creating a user with recommended role
echo "<h2>Test User Creation</h2>";

if (isset($_GET['test_create'])) {
    try {
        $testEmail = "<EMAIL>";
        
        // Delete if exists
        $pdo->prepare("DELETE FROM users WHERE email = ?")->execute([$testEmail]);
        
        // Create new user
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $result = $stmt->execute([
            'Sample Test User',
            $testEmail,
            password_hash('test123', PASSWORD_DEFAULT),
            $recommendedRole
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Successfully created user with role '$recommendedRole'</p>";
            
            // Verify
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
            $stmt->execute([$testEmail]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Created user:</strong></p>";
            echo "<ul>";
            echo "<li>ID: " . $user['id'] . "</li>";
            echo "<li>Name: " . htmlspecialchars($user['name']) . "</li>";
            echo "<li>Email: " . htmlspecialchars($user['email']) . "</li>";
            echo "<li>Role: " . htmlspecialchars($user['role']) . "</li>";
            echo "</ul>";
            
            // Clean up
            $pdo->prepare("DELETE FROM users WHERE email = ?")->execute([$testEmail]);
            echo "<p style='color: blue;'>ℹ Test user cleaned up</p>";
            
        } else {
            echo "<p style='color: red;'>✗ Failed to create user</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Error creating user: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p><a href='?test_create=1'>Test Create User with Role '$recommendedRole'</a></p>";
}

// Show current users
echo "<h2>Current Users</h2>";
$stmt = $pdo->query("SELECT id, name, email, role, created_at FROM users ORDER BY created_at DESC LIMIT 10");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($users) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Created</th>";
    echo "</tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td><strong>" . htmlspecialchars($user['role']) . "</strong></td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($user['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No users found</p>";
}

// Next steps
echo "<h2>Next Steps</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>To Fix Create Sample Data:</h3>";
echo "<ol>";
echo "<li>Use role value: <strong>'$recommendedRole'</strong></li>";
echo "<li><a href='create-sample-data.php'>Try Create Sample Data Again</a></li>";
echo "<li><a href='fix-database-schema.php'>Fix Database Schema if needed</a></li>";
echo "</ol>";
echo "</div>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
