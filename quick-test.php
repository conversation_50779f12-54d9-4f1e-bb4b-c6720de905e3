<?php
/**
 * Quick Test for Photo Display Feature
 */

require_once 'app/init.php';

echo "<h1>Quick Test - Photo Display Feature</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection</h2>";
try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connected</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check Admin User
echo "<h2>2. Admin User Check</h2>";
$stmt = $pdo->query("SELECT id, name, email FROM users WHERE role = 'admin' LIMIT 1");
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if ($admin) {
    echo "<p style='color: green;'>✓ Admin user found: " . $admin['email'] . "</p>";
} else {
    echo "<p style='color: red;'>✗ No admin user found</p>";
    echo "<p><a href='fix-admin-account.php'>Create Admin Account</a></p>";
}

// Test 3: Check Complaints with Photos
echo "<h2>3. Complaints with Photos</h2>";
$stmt = $pdo->query("
    SELECT c.*, u.name as user_name, u.email as user_email, cat.name as category_name 
    FROM complaints c 
    LEFT JOIN users u ON c.user_id = u.id 
    LEFT JOIN categories cat ON c.category_id = cat.id 
    WHERE c.photo IS NOT NULL AND c.photo != ''
    ORDER BY c.created_at DESC
    LIMIT 5
");
$complaints = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($complaints) > 0) {
    echo "<p style='color: green;'>✓ Found " . count($complaints) . " complaints with photos</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Title</th><th>User</th><th>Photo</th><th>File Exists</th><th>Preview</th>";
    echo "</tr>";
    
    foreach ($complaints as $complaint) {
        $photoPath = "public/uploads/" . $complaint['photo'];
        $photoExists = file_exists($photoPath);
        
        echo "<tr>";
        echo "<td>#" . str_pad($complaint['id'], 6, '0', STR_PAD_LEFT) . "</td>";
        echo "<td>" . htmlspecialchars(substr($complaint['title'], 0, 30)) . "...</td>";
        echo "<td>" . htmlspecialchars($complaint['user_name']) . "</td>";
        echo "<td>" . htmlspecialchars($complaint['photo']) . "</td>";
        echo "<td>" . ($photoExists ? "<span style='color: green;'>✓ Yes</span>" : "<span style='color: red;'>✗ No</span>") . "</td>";
        echo "<td>";
        
        if ($photoExists) {
            echo "<img src='" . BASE_URL . "public/uploads/" . $complaint['photo'] . "' style='max-width: 80px; max-height: 50px; border: 1px solid #ddd;' alt='Preview'>";
        } else {
            echo "<span style='color: #999;'>No preview</span>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No complaints with photos found</p>";
    echo "<p><a href='create-sample-data.php'>Create Sample Data</a></p>";
}

// Test 4: Upload Directory
echo "<h2>4. Upload Directory</h2>";
$uploadDir = 'public/uploads';
if (is_dir($uploadDir)) {
    echo "<p style='color: green;'>✓ Upload directory exists</p>";
    
    $files = glob($uploadDir . '/*');
    echo "<p>Files in directory: " . count($files) . "</p>";
    
    if (count($files) > 0) {
        echo "<ul>";
        foreach (array_slice($files, 0, 5) as $file) {
            $filename = basename($file);
            $filesize = round(filesize($file) / 1024, 2);
            echo "<li>$filename ({$filesize} KB)</li>";
        }
        if (count($files) > 5) {
            echo "<li>... and " . (count($files) - 5) . " more files</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: red;'>✗ Upload directory not found</p>";
    echo "<p><a href='create-sample-data.php'>Create Upload Directory</a></p>";
}

// Test 5: Model Query Test
echo "<h2>5. Model Query Test</h2>";
try {
    $complaintModel = new Complaint();
    $allComplaints = $complaintModel->getComplaints();
    
    if (!empty($allComplaints)) {
        $firstComplaint = $allComplaints[0];
        
        echo "<p style='color: green;'>✓ Complaint model working</p>";
        echo "<p>Sample complaint data:</p>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> " . $firstComplaint->id . "</li>";
        echo "<li><strong>Title:</strong> " . htmlspecialchars($firstComplaint->title) . "</li>";
        echo "<li><strong>User Name:</strong> " . (isset($firstComplaint->user_name) ? htmlspecialchars($firstComplaint->user_name) : 'N/A') . "</li>";
        echo "<li><strong>User Email:</strong> " . (isset($firstComplaint->user_email) ? htmlspecialchars($firstComplaint->user_email) : 'N/A') . "</li>";
        echo "<li><strong>Category:</strong> " . (isset($firstComplaint->category_name) ? htmlspecialchars($firstComplaint->category_name) : 'N/A') . "</li>";
        echo "<li><strong>Photo:</strong> " . (isset($firstComplaint->photo) ? htmlspecialchars($firstComplaint->photo) : 'None') . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: orange;'>⚠ No complaints found in database</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Model error: " . $e->getMessage() . "</p>";
}

// Test 6: Admin URLs
echo "<h2>6. Admin Access URLs</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>Step-by-step Admin Login:</h3>";
echo "<ol>";
echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin' target='_blank'>Step 1: Enter Admin PIN (2024)</a></li>";
echo "<li>Step 2: Login with admin credentials</li>";
echo "<li><a href='" . BASE_URL . "index.php?url=admin/complaints' target='_blank'>Step 3: View Complaints (after login)</a></li>";
echo "</ol>";
echo "</div>";

// Test 7: Quick Actions
echo "<h2>7. Quick Actions</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='fix-admin-account.php' class='btn btn-primary'>Fix Admin Account</a>";
echo "<a href='create-sample-data.php' class='btn btn-success'>Create Sample Data</a>";
echo "<a href='test-photo-display.php' class='btn btn-info'>Test Photo Display</a>";
echo "<a href='comprehensive-check.php' class='btn btn-warning'>System Check</a>";
echo "</div>";

// Summary
echo "<h2>8. Summary</h2>";
$issues = [];

if (!$admin) $issues[] = "No admin user found";
if (count($complaints) == 0) $issues[] = "No complaints with photos";
if (!is_dir($uploadDir)) $issues[] = "Upload directory missing";

if (empty($issues)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h3>✅ All Tests Passed!</h3>";
    echo "<p>The photo display feature should be working correctly. You can now:</p>";
    echo "<ul>";
    echo "<li>Login as admin using PIN 2024</li>";
    echo "<li>View complaints with photos in admin dashboard</li>";
    echo "<li>Click 'Lihat Foto' to view photos in modal</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h3>⚠ Issues Found:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "<p>Please fix these issues before testing the photo display feature.</p>";
    echo "</div>";
}

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
.btn { background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px; }
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-info { background: #17a2b8; }
.btn-warning { background: #ffc107; color: #212529; }
.btn:hover { opacity: 0.8; color: white; text-decoration: none; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
img { border-radius: 4px; }
</style>";
?>
