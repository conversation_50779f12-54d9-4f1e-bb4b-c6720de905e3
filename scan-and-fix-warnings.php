<?php
/**
 * <PERSON>an and Fix PHP 8.2 Warnings
 */

echo "<h1>Scan and Fix PHP 8.2 Warnings</h1>";

// Function to scan files for deprecated usage
function scanFileForDeprecated($filePath) {
    if (!file_exists($filePath)) {
        return [];
    }
    
    $content = file_get_contents($filePath);
    $issues = [];
    
    // Check for FILTER_SANITIZE_STRING
    if (strpos($content, 'FILTER_SANITIZE_STRING') !== false) {
        $issues[] = 'Uses deprecated FILTER_SANITIZE_STRING';
    }
    
    // Check for FILTER_SANITIZE_STRIPPED
    if (strpos($content, 'FILTER_SANITIZE_STRIPPED') !== false) {
        $issues[] = 'Uses deprecated FILTER_SANITIZE_STRIPPED';
    }
    
    // Check for undefined array key patterns
    if (preg_match('/\$data\[\'[^\']+\'\]/', $content) && !preg_match('/isset\(\$data\[/', $content)) {
        $issues[] = 'Potential undefined array key usage';
    }
    
    return $issues;
}

// Function to get all PHP files
function getAllPHPFiles($directory) {
    $files = [];
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->getExtension() === 'php') {
            $files[] = $file->getPathname();
        }
    }
    
    return $files;
}

// Scan all PHP files
echo "<h2>Scanning PHP Files for Issues</h2>";

$directories = ['app/controllers', 'app/models', 'app/views', 'app/helpers'];
$allIssues = [];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        echo "<h3>Scanning: $dir</h3>";
        $files = getAllPHPFiles($dir);
        
        foreach ($files as $file) {
            $issues = scanFileForDeprecated($file);
            if (!empty($issues)) {
                $allIssues[$file] = $issues;
                echo "<p style='color: orange;'>⚠ $file:</p>";
                echo "<ul>";
                foreach ($issues as $issue) {
                    echo "<li>$issue</li>";
                }
                echo "</ul>";
            }
        }
    }
}

if (empty($allIssues)) {
    echo "<p style='color: green;'>✓ No deprecated usage found in scanned files</p>";
} else {
    echo "<h2>Summary of Issues Found</h2>";
    echo "<p>Found issues in " . count($allIssues) . " files:</p>";
    
    foreach ($allIssues as $file => $issues) {
        echo "<p><strong>$file:</strong> " . implode(', ', $issues) . "</p>";
    }
}

// Check specific common issues
echo "<h2>Specific Issue Checks</h2>";

// Check header.php for title issue
$headerFile = 'app/views/inc/header.php';
if (file_exists($headerFile)) {
    $content = file_get_contents($headerFile);
    if (strpos($content, "echo \$data['title']") !== false && strpos($content, 'isset($data[\'title\'])') === false) {
        echo "<p style='color: red;'>✗ Header.php has undefined array key issue for title</p>";
        echo "<p><a href='?fix_header=1'>Fix Header Title Issue</a></p>";
    } else {
        echo "<p style='color: green;'>✓ Header.php title issue is fixed</p>";
    }
}

// Handle fixes
if (isset($_GET['fix_header'])) {
    $headerFile = 'app/views/inc/header.php';
    $content = file_get_contents($headerFile);
    
    // Fix title issue
    $content = str_replace(
        "echo \$data['title'];",
        "echo isset(\$data['title']) ? \$data['title'] : 'Sistem Pengaduan Warga';",
        $content
    );
    
    if (file_put_contents($headerFile, $content)) {
        echo "<p style='color: green;'>✓ Fixed header title issue</p>";
        echo "<script>setTimeout(function(){ location.href = '?'; }, 2000);</script>";
    } else {
        echo "<p style='color: red;'>✗ Failed to fix header title issue</p>";
    }
}

// Check for any remaining FILTER_SANITIZE_STRING usage
echo "<h2>Deprecated Filter Check</h2>";
$deprecatedFound = false;

$allFiles = [];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $allFiles = array_merge($allFiles, getAllPHPFiles($dir));
    }
}

foreach ($allFiles as $file) {
    $content = file_get_contents($file);
    if (strpos($content, 'FILTER_SANITIZE_STRING') !== false) {
        echo "<p style='color: red;'>✗ Found FILTER_SANITIZE_STRING in: $file</p>";
        $deprecatedFound = true;
    }
}

if (!$deprecatedFound) {
    echo "<p style='color: green;'>✓ No FILTER_SANITIZE_STRING usage found</p>";
}

// Check error logs
echo "<h2>Recent Error Logs</h2>";
$logFile = 'logs/app.log';
if (file_exists($logFile)) {
    $logs = file($logFile, FILE_IGNORE_NEW_LINES);
    $recentLogs = array_slice($logs, -10); // Last 10 entries
    
    echo "<h3>Last 10 Log Entries:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLogs as $log) {
        if (strpos($log, 'FILTER_SANITIZE_STRING') !== false) {
            echo "<span style='color: red;'>" . htmlspecialchars($log) . "</span>\n";
        } elseif (strpos($log, 'Undefined array key') !== false) {
            echo "<span style='color: orange;'>" . htmlspecialchars($log) . "</span>\n";
        } else {
            echo htmlspecialchars($log) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>No log file found</p>";
}

// PHP Configuration Check
echo "<h2>PHP Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Error Reporting:</strong> " . error_reporting() . "</p>";
echo "<p><strong>Display Errors:</strong> " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>";
echo "<p><strong>Log Errors:</strong> " . (ini_get('log_errors') ? 'On' : 'Off') . "</p>";

// Recommendations
echo "<h2>Recommendations</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>To Fix Remaining Issues:</h3>";
echo "<ol>";
echo "<li>Replace all <code>FILTER_SANITIZE_STRING</code> with <code>sanitizePost()</code> or <code>sanitizeInput()</code></li>";
echo "<li>Add <code>isset()</code> checks for array keys before accessing them</li>";
echo "<li>Use null coalescing operator <code>??</code> for default values</li>";
echo "<li>Test all forms and user inputs after changes</li>";
echo "</ol>";
echo "</div>";

// Quick Actions
echo "<h2>Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='fix-admin-account.php'>Fix Admin Account</a></li>";
echo "<li><a href='comprehensive-check.php'>Comprehensive System Check</a></li>";
echo "<li><a href='test-php8-compatibility.php'>PHP 8 Compatibility Test</a></li>";
echo "<li><a href='" . (defined('BASE_URL') ? BASE_URL : '/') . "'>Go to Application</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>";
?>
