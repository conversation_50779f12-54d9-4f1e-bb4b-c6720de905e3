<?php
/**
 * Test Photo Display in Admin Dashboard
 */

require_once 'app/init.php';

echo "<h1>Test Photo Display in Admin Dashboard</h1>";

// Test database connection
try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Check complaints with photos
echo "<h2>Complaints with Photos</h2>";
$stmt = $pdo->query("
    SELECT c.*, u.name as user_name, u.email as user_email, cat.name as category_name 
    FROM complaints c 
    LEFT JOIN users u ON c.user_id = u.id 
    LEFT JOIN categories cat ON c.category_id = cat.id 
    WHERE c.photo IS NOT NULL AND c.photo != ''
    ORDER BY c.created_at DESC
");
$complaintsWithPhotos = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($complaintsWithPhotos) > 0) {
    echo "<p style='color: green;'>✓ Found " . count($complaintsWithPhotos) . " complaints with photos</p>";
    
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Title</th><th>User</th><th>Photo</th><th>Status</th><th>Preview</th>";
    echo "</tr>";
    
    foreach ($complaintsWithPhotos as $complaint) {
        $photoPath = "public/uploads/" . $complaint['photo'];
        $photoExists = file_exists($photoPath);
        
        echo "<tr>";
        echo "<td>#" . str_pad($complaint['id'], 6, '0', STR_PAD_LEFT) . "</td>";
        echo "<td>" . htmlspecialchars($complaint['title']) . "</td>";
        echo "<td>" . htmlspecialchars($complaint['user_name']) . "<br><small>" . htmlspecialchars($complaint['user_email']) . "</small></td>";
        echo "<td>" . htmlspecialchars($complaint['photo']) . "</td>";
        echo "<td>" . ucfirst($complaint['status']) . "</td>";
        echo "<td>";
        
        if ($photoExists) {
            echo "<img src='" . BASE_URL . "public/uploads/" . $complaint['photo'] . "' style='max-width: 100px; max-height: 60px;' alt='Preview'>";
            echo "<br><span style='color: green;'>✓ File exists</span>";
        } else {
            echo "<span style='color: red;'>✗ File not found</span>";
            echo "<br><small>Path: $photoPath</small>";
        }
        
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No complaints with photos found</p>";
    echo "<p><a href='?create_sample=1'>Create Sample Complaint with Photo</a></p>";
}

// Check all complaints
echo "<h2>All Complaints Summary</h2>";
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN photo IS NOT NULL AND photo != '' THEN 1 ELSE 0 END) as with_photo,
        SUM(CASE WHEN photo IS NULL OR photo = '' THEN 1 ELSE 0 END) as without_photo
    FROM complaints
");
$summary = $stmt->fetch(PDO::FETCH_ASSOC);

echo "<ul>";
echo "<li><strong>Total Complaints:</strong> " . $summary['total'] . "</li>";
echo "<li><strong>With Photo:</strong> " . $summary['with_photo'] . "</li>";
echo "<li><strong>Without Photo:</strong> " . $summary['without_photo'] . "</li>";
echo "</ul>";

// Check upload directory
echo "<h2>Upload Directory Check</h2>";
$uploadDir = 'public/uploads';
if (is_dir($uploadDir)) {
    echo "<p style='color: green;'>✓ Upload directory exists: $uploadDir</p>";
    
    $files = glob($uploadDir . '/*');
    echo "<p>Files in upload directory: " . count($files) . "</p>";
    
    if (count($files) > 0) {
        echo "<h3>Upload Directory Contents:</h3>";
        echo "<ul>";
        foreach ($files as $file) {
            $filename = basename($file);
            $filesize = formatFileSize(filesize($file));
            echo "<li>$filename ($filesize)</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<p style='color: red;'>✗ Upload directory not found: $uploadDir</p>";
    echo "<p><a href='?create_upload_dir=1'>Create Upload Directory</a></p>";
}

// Handle create upload directory
if (isset($_GET['create_upload_dir'])) {
    if (mkdir($uploadDir, 0755, true)) {
        echo "<p style='color: green;'>✓ Upload directory created</p>";
        
        // Create .htaccess for uploads
        $htaccess = "Options -Indexes\n";
        $htaccess .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
        $htaccess .= "    Order Allow,Deny\n";
        $htaccess .= "    Allow from all\n";
        $htaccess .= "</FilesMatch>";
        
        file_put_contents($uploadDir . '/.htaccess', $htaccess);
        echo "<p style='color: green;'>✓ .htaccess created for uploads</p>";
        
        echo "<script>setTimeout(function(){ location.href = '?'; }, 2000);</script>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create upload directory</p>";
    }
}

// Handle create sample complaint
if (isset($_GET['create_sample'])) {
    echo "<h3>Creating sample complaint with photo...</h3>";
    
    // First, check if we have a user
    $stmt = $pdo->query("SELECT id FROM users WHERE role = 'user' LIMIT 1");
    $user = $stmt->fetch();
    
    if (!$user) {
        // Create a sample user
        $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Sample User', '<EMAIL>', $hashedPassword, 'user']);
        $userId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Created sample user</p>";
    } else {
        $userId = $user['id'];
    }
    
    // Get a category
    $stmt = $pdo->query("SELECT id FROM categories LIMIT 1");
    $category = $stmt->fetch();
    
    if (!$category) {
        $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        $stmt->execute(['Sample Category', 'Sample category for testing']);
        $categoryId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Created sample category</p>";
    } else {
        $categoryId = $category['id'];
    }
    
    // Create sample photo (placeholder)
    $photoName = 'sample_' . time() . '.jpg';
    $photoPath = $uploadDir . '/' . $photoName;
    
    // Create a simple placeholder image
    $placeholderContent = base64_decode('/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A');
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    file_put_contents($photoPath, $placeholderContent);
    
    // Insert sample complaint
    $stmt = $pdo->prepare("
        INSERT INTO complaints (user_id, category_id, title, description, location, photo, status) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([
        $userId,
        $categoryId,
        'Sample Complaint with Photo',
        'This is a sample complaint created for testing photo display functionality.',
        'Sample Location',
        $photoName,
        'pending'
    ]);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Sample complaint created with photo</p>";
        echo "<script>setTimeout(function(){ location.href = '?'; }, 3000);</script>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create sample complaint</p>";
    }
}

// Test admin complaints page
echo "<h2>Test Admin Complaints Page</h2>";
echo "<p>After fixing the issues, test the admin complaints page:</p>";
echo "<ol>";
echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin' target='_blank'>Login as Admin (PIN: 2024)</a></li>";
echo "<li>Navigate to Complaints section</li>";
echo "<li>Check if photos are displayed correctly</li>";
echo "<li>Click 'Lihat Foto' button to view photos in modal</li>";
echo "</ol>";

// Quick fixes
echo "<h2>Quick Fixes</h2>";
echo "<ul>";
echo "<li><a href='fix-admin-account.php'>Fix Admin Account</a></li>";
echo "<li><a href='?create_upload_dir=1'>Create Upload Directory</a></li>";
echo "<li><a href='?create_sample=1'>Create Sample Complaint with Photo</a></li>";
echo "<li><a href='comprehensive-check.php'>System Check</a></li>";
echo "</ul>";

// Helper function
function formatFileSize($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
img { border: 1px solid #ddd; border-radius: 4px; }
</style>";
?>
