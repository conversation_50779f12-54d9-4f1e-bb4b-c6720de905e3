/* Custom styles for Pengaduan Warga */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    color: #2c3e50 !important;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.btn-primary {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    border-color: #5a6fd8;
}

.status-pending {
    color: #ffc107;
}

.status-process {
    color: #17a2b8;
}

.status-completed {
    color: #28a745;
}

.status-rejected {
    color: #dc3545;
}

.footer {
    background-color: #2c3e50;
    color: white;
    padding: 40px 0;
    margin-top: 50px;
}

.complaint-card {
    border-left: 4px solid #667eea;
}

.admin-sidebar {
    background-color: #343a40;
    min-height: 100vh;
}

.admin-sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1rem;
}

.admin-sidebar .nav-link:hover {
    color: #fff;
    background-color: #495057;
}

.admin-sidebar .nav-link.active {
    color: #fff;
    background-color: #667eea;
}

@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
