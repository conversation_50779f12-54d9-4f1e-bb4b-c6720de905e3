<?php
/**
 * Fix Database Schema Issues
 */

require_once 'app/config/config.php';

echo "<h1>Fix Database Schema</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Check and fix role column
echo "<h2>1. Fix Role Column</h2>";

$stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
$roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);

if ($roleColumn) {
    echo "<p><strong>Current role type:</strong> " . $roleColumn['Type'] . "</p>";
    
    if (strpos($roleColumn['Type'], 'enum') !== false) {
        preg_match_all("/'([^']+)'/", $roleColumn['Type'], $matches);
        $enumValues = $matches[1];
        echo "<p><strong>Current ENUM values:</strong> " . implode(', ', $enumValues) . "</p>";
        
        // Check if we need to update ENUM values
        $needsUpdate = !in_array('user', $enumValues) || !in_array('admin', $enumValues);
        
        if ($needsUpdate) {
            echo "<p style='color: orange;'>⚠ Role ENUM needs updating</p>";
            
            if (isset($_GET['fix_role'])) {
                try {
                    $pdo->exec("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'user') NOT NULL DEFAULT 'user'");
                    echo "<p style='color: green;'>✓ Role ENUM updated to include 'admin' and 'user'</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>✗ Failed to update role ENUM: " . $e->getMessage() . "</p>";
                    
                    // Try alternative approach - change to VARCHAR
                    try {
                        echo "<p>Trying alternative approach (VARCHAR)...</p>";
                        $pdo->exec("ALTER TABLE users MODIFY COLUMN role VARCHAR(20) NOT NULL DEFAULT 'user'");
                        echo "<p style='color: green;'>✓ Role column changed to VARCHAR</p>";
                    } catch (PDOException $e2) {
                        echo "<p style='color: red;'>✗ Failed to change to VARCHAR: " . $e2->getMessage() . "</p>";
                    }
                }
            } else {
                echo "<p><a href='?fix_role=1'>Fix Role ENUM Values</a></p>";
            }
        } else {
            echo "<p style='color: green;'>✓ Role ENUM values are correct</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Role column is flexible (not ENUM)</p>";
    }
}

// Check and fix photo column in complaints
echo "<h2>2. Check Photo Column in Complaints</h2>";

$stmt = $pdo->query("SHOW COLUMNS FROM complaints LIKE 'photo'");
$photoColumn = $stmt->fetch(PDO::FETCH_ASSOC);

if ($photoColumn) {
    echo "<p style='color: green;'>✓ Photo column exists: " . $photoColumn['Type'] . "</p>";
} else {
    echo "<p style='color: orange;'>⚠ Photo column missing</p>";
    
    if (isset($_GET['add_photo_column'])) {
        try {
            $pdo->exec("ALTER TABLE complaints ADD COLUMN photo VARCHAR(255) NULL AFTER location");
            echo "<p style='color: green;'>✓ Photo column added to complaints table</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Failed to add photo column: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p><a href='?add_photo_column=1'>Add Photo Column</a></p>";
    }
}

// Check admin user
echo "<h2>3. Check Admin User</h2>";

$stmt = $pdo->query("SELECT id, name, email, role FROM users WHERE role = 'admin' OR email = '<EMAIL>'");
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if ($admin) {
    echo "<p style='color: green;'>✓ Admin user found: " . $admin['email'] . " (role: " . $admin['role'] . ")</p>";
} else {
    echo "<p style='color: orange;'>⚠ No admin user found</p>";
    
    if (isset($_GET['create_admin'])) {
        try {
            $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['Administrator', '<EMAIL>', $hashedPassword, 'admin']);
            echo "<p style='color: green;'>✓ Admin user created (email: <EMAIL>, password: admin123)</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Failed to create admin user: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p><a href='?create_admin=1'>Create Admin User</a></p>";
    }
}

// Check categories
echo "<h2>4. Check Categories</h2>";

$stmt = $pdo->query("SELECT COUNT(*) as count FROM categories");
$categoryCount = $stmt->fetchColumn();

if ($categoryCount > 0) {
    echo "<p style='color: green;'>✓ Found $categoryCount categories</p>";
} else {
    echo "<p style='color: orange;'>⚠ No categories found</p>";
    
    if (isset($_GET['create_categories'])) {
        $defaultCategories = [
            ['Infrastruktur', 'Masalah jalan, jembatan, dan fasilitas umum'],
            ['Kebersihan', 'Masalah sampah dan kebersihan lingkungan'],
            ['Keamanan', 'Masalah keamanan dan ketertiban'],
            ['Pelayanan Publik', 'Masalah pelayanan administrasi dan publik'],
            ['Lainnya', 'Kategori lainnya yang tidak termasuk di atas']
        ];
        
        $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
        
        foreach ($defaultCategories as $category) {
            try {
                $stmt->execute($category);
                echo "<p style='color: green;'>✓ Created category: " . $category[0] . "</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Failed to create category " . $category[0] . ": " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p><a href='?create_categories=1'>Create Default Categories</a></p>";
    }
}

// Check upload directory
echo "<h2>5. Check Upload Directory</h2>";

$uploadDir = 'public/uploads';
if (is_dir($uploadDir)) {
    echo "<p style='color: green;'>✓ Upload directory exists</p>";
    
    if (!file_exists($uploadDir . '/.htaccess')) {
        if (isset($_GET['create_htaccess'])) {
            $htaccess = "Options -Indexes\n";
            $htaccess .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
            $htaccess .= "    Order Allow,Deny\n";
            $htaccess .= "    Allow from all\n";
            $htaccess .= "</FilesMatch>";
            
            file_put_contents($uploadDir . '/.htaccess', $htaccess);
            echo "<p style='color: green;'>✓ Created .htaccess for uploads</p>";
        } else {
            echo "<p style='color: orange;'>⚠ .htaccess missing in uploads</p>";
            echo "<p><a href='?create_htaccess=1'>Create .htaccess</a></p>";
        }
    } else {
        echo "<p style='color: green;'>✓ .htaccess exists in uploads</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ Upload directory missing</p>";
    
    if (isset($_GET['create_upload_dir'])) {
        if (mkdir($uploadDir, 0755, true)) {
            echo "<p style='color: green;'>✓ Upload directory created</p>";
            
            $htaccess = "Options -Indexes\n";
            $htaccess .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
            $htaccess .= "    Order Allow,Deny\n";
            $htaccess .= "    Allow from all\n";
            $htaccess .= "</FilesMatch>";
            
            file_put_contents($uploadDir . '/.htaccess', $htaccess);
            echo "<p style='color: green;'>✓ Created .htaccess for uploads</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create upload directory</p>";
        }
    } else {
        echo "<p><a href='?create_upload_dir=1'>Create Upload Directory</a></p>";
    }
}

// Summary and next steps
echo "<h2>6. Summary & Next Steps</h2>";

$issues = [];
if (!$roleColumn || (strpos($roleColumn['Type'], 'enum') !== false && !in_array('user', $enumValues ?? []))) {
    $issues[] = "Role column needs fixing";
}
if (!$photoColumn) {
    $issues[] = "Photo column missing";
}
if (!$admin) {
    $issues[] = "Admin user missing";
}
if ($categoryCount == 0) {
    $issues[] = "No categories";
}
if (!is_dir($uploadDir)) {
    $issues[] = "Upload directory missing";
}

if (empty($issues)) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<h3>✅ Database Schema is Ready!</h3>";
    echo "<p>All required components are in place. You can now:</p>";
    echo "<ul>";
    echo "<li><a href='create-sample-data.php'>Create Sample Data</a></li>";
    echo "<li><a href='quick-test.php'>Run Quick Test</a></li>";
    echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin'>Test Admin Login</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h3>⚠ Issues Found:</h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h2>Quick Fix All</h2>";
echo "<p><a href='?fix_role=1&add_photo_column=1&create_admin=1&create_categories=1&create_upload_dir=1&create_htaccess=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Fix All Issues</a></p>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
