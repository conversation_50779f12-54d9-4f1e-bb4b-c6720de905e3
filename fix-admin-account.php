<?php
/**
 * Fix Admin Account
 */

require_once 'app/config/config.php';

echo "<h1>Fix Admin Account</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Check existing admin users
echo "<h2>Current Admin Users</h2>";
$stmt = $pdo->query("SELECT id, name, email, role, created_at FROM users WHERE role = 'admin'");
$admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($admins) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Created</th><th>Action</th></tr>";
    
    foreach ($admins as $admin) {
        echo "<tr>";
        echo "<td>" . $admin['id'] . "</td>";
        echo "<td>" . htmlspecialchars($admin['name']) . "</td>";
        echo "<td>" . htmlspecialchars($admin['email']) . "</td>";
        echo "<td>" . $admin['role'] . "</td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($admin['created_at'])) . "</td>";
        echo "<td><a href='?reset_password=" . $admin['id'] . "' onclick='return confirm(\"Reset password to admin123?\")'>Reset Password</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠ No admin users found</p>";
}

// Handle password reset
if (isset($_GET['reset_password'])) {
    $adminId = (int)$_GET['reset_password'];
    $newPassword = password_hash('admin123', PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ? AND role = 'admin'");
    if ($stmt->execute([$newPassword, $adminId])) {
        echo "<p style='color: green;'>✓ Password reset to 'admin123' for admin ID: $adminId</p>";
        echo "<script>setTimeout(function(){ location.href = '?'; }, 2000);</script>";
    } else {
        echo "<p style='color: red;'>✗ Failed to reset password</p>";
    }
}

// Handle create admin
if (isset($_GET['create_admin'])) {
    echo "<h3>Creating default admin account...</h3>";
    
    // Check if admin email already exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    
    if ($stmt->fetchColumn() > 0) {
        echo "<p style='color: orange;'>⚠ Admin email already exists. Use reset password instead.</p>";
    } else {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (name, email, password, role) VALUES (?, ?, ?, ?)");
        if ($stmt->execute(['Administrator', '<EMAIL>', $hashedPassword, 'admin'])) {
            echo "<p style='color: green;'>✓ Admin account created successfully!</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Password:</strong> admin123</p>";
            echo "<script>setTimeout(function(){ location.href = '?'; }, 3000);</script>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create admin account</p>";
        }
    }
}

// Handle update admin email
if (isset($_POST['update_admin'])) {
    $adminId = (int)$_POST['admin_id'];
    $newEmail = trim($_POST['new_email']);
    $newName = trim($_POST['new_name']);
    
    if (!empty($newEmail) && !empty($newName)) {
        // Check if email already exists (excluding current admin)
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ? AND id != ?");
        $stmt->execute([$newEmail, $adminId]);
        
        if ($stmt->fetchColumn() > 0) {
            echo "<p style='color: red;'>✗ Email already exists</p>";
        } else {
            $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ? WHERE id = ? AND role = 'admin'");
            if ($stmt->execute([$newName, $newEmail, $adminId])) {
                echo "<p style='color: green;'>✓ Admin account updated successfully!</p>";
                echo "<script>setTimeout(function(){ location.href = '?'; }, 2000);</script>";
            } else {
                echo "<p style='color: red;'>✗ Failed to update admin account</p>";
            }
        }
    }
}

echo "<h2>Admin Account Management</h2>";

if (count($admins) == 0) {
    echo "<p><a href='?create_admin=1' class='btn btn-primary'>Create Default Admin Account</a></p>";
} else {
    echo "<h3>Update Admin Account</h3>";
    $admin = $admins[0]; // Use first admin
    echo "<form method='post'>";
    echo "<input type='hidden' name='admin_id' value='" . $admin['id'] . "'>";
    echo "<p><label>Name: <input type='text' name='new_name' value='" . htmlspecialchars($admin['name']) . "' required></label></p>";
    echo "<p><label>Email: <input type='email' name='new_email' value='" . htmlspecialchars($admin['email']) . "' required></label></p>";
    echo "<p><button type='submit' name='update_admin'>Update Admin Account</button></p>";
    echo "</form>";
}

echo "<h2>Test Admin Login</h2>";
if (count($admins) > 0) {
    $admin = $admins[0];
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
    echo "<h3>Admin Login Credentials:</h3>";
    echo "<p><strong>Email:</strong> " . htmlspecialchars($admin['email']) . "</p>";
    echo "<p><strong>Password:</strong> admin123 (if reset)</p>";
    echo "<p><strong>PIN:</strong> 2024</p>";
    echo "</div>";
    
    echo "<h3>Login Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin' target='_blank'>Step 1: Enter PIN (2024)</a></li>";
    echo "<li>Step 2: Login with email and password</li>";
    echo "<li><a href='" . BASE_URL . "index.php?url=admin' target='_blank'>Step 3: Access Admin Dashboard</a></li>";
    echo "</ol>";
}

echo "<h2>Password Hash Test</h2>";
$testPassword = 'admin123';
$hash1 = password_hash($testPassword, PASSWORD_DEFAULT);
$hash2 = password_hash($testPassword, PASSWORD_DEFAULT);

echo "<p><strong>Test Password:</strong> $testPassword</p>";
echo "<p><strong>Hash 1:</strong> $hash1</p>";
echo "<p><strong>Hash 2:</strong> $hash2</p>";
echo "<p><strong>Verify Hash 1:</strong> " . (password_verify($testPassword, $hash1) ? "✓ Valid" : "✗ Invalid") . "</p>";
echo "<p><strong>Verify Hash 2:</strong> " . (password_verify($testPassword, $hash2) ? "✓ Valid" : "✗ Invalid") . "</p>";

echo "<h2>Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='?create_admin=1'>Create Default Admin Account</a></li>";
if (count($admins) > 0) {
    echo "<li><a href='?reset_password=" . $admins[0]['id'] . "' onclick='return confirm(\"Reset password to admin123?\")'>Reset Admin Password</a></li>";
}
echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin'>Test Admin Login</a></li>";
echo "<li><a href='comprehensive-check.php'>System Check</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
.btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; }
.btn:hover { background: #0056b3; color: white; }
input[type='text'], input[type='email'] { padding: 5px; width: 200px; }
button { background: #28a745; color: white; padding: 8px 15px; border: none; border-radius: 3px; cursor: pointer; }
button:hover { background: #218838; }
</style>";
?>
