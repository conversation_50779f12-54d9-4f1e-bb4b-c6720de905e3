<?php
class Category {
    private $db;
    
    public function __construct() {
        $this->db = new Database;
    }
    
    // Get all categories
    public function getCategories() {
        $this->db->query('SELECT * FROM categories ORDER BY name ASC');
        
        $results = $this->db->resultSet();
        return $results;
    }
    
    // Get category by ID
    public function getCategoryById($id) {
        $this->db->query('SELECT * FROM categories WHERE id = :id');
        $this->db->bind(':id', $id);
        
        $row = $this->db->single();
        return $row;
    }
    
    // Add category
    public function addCategory($data) {
        $this->db->query('INSERT INTO categories (name, description) VALUES(:name, :description)');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    // Update category
    public function updateCategory($data) {
        $this->db->query('UPDATE categories SET name = :name, description = :description WHERE id = :id');
        
        $this->db->bind(':id', $data['id']);
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    // Delete category
    public function deleteCategory($id) {
        $this->db->query('DELETE FROM categories WHERE id = :id');
        $this->db->bind(':id', $id);
        
        if ($this->db->execute()) {
            return true;
        } else {
            return false;
        }
    }
    
    // Check if category has complaints
    public function hasComplaints($id) {
        $this->db->query('SELECT COUNT(*) as count FROM complaints WHERE category_id = :id');
        $this->db->bind(':id', $id);
        
        $result = $this->db->single();
        return $result->count > 0;
    }
}
?>
