<?php
echo "<h1>Test Access</h1>";
echo "<p>If you can see this, the basic access works.</p>";

echo "<h2>Available Pages:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>Homepage (index.php)</a></li>";
echo "<li><a href='login.php'>Login (login.php)</a></li>";
echo "<li><a href='register.php'>Register (register.php)</a></li>";
echo "<li><a href='admin.php'>Admin (admin.php)</a></li>";
echo "</ul>";

echo "<h2>With URL Rewrite (if mod_rewrite works):</h2>";
echo "<ul>";
echo "<li><a href='auth/login'>Login (auth/login)</a></li>";
echo "<li><a href='auth/register'>Register (auth/register)</a></li>";
echo "<li><a href='admin'>Admin (admin)</a></li>";
echo "</ul>";

echo "<h2>Debug Info:</h2>";
echo "<p>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p>GET url: " . ($_GET['url'] ?? 'Not set') . "</p>";
echo "<p>mod_rewrite test: ";
if (function_exists('apache_get_modules')) {
    echo in_array('mod_rewrite', apache_get_modules()) ? 'Available' : 'Not available';
} else {
    echo 'Cannot detect';
}
echo "</p>";
?>
