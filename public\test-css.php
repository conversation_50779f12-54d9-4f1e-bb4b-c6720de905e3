<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container mt-5">
        <h1>CSS Loading Test</h1>
        
        <div class="alert alert-info">
            <h4>Testing CSS Files:</h4>
            <ul>
                <li>Bootstrap CSS: <span id="bootstrap-status">Loading...</span></li>
                <li>Font Awesome: <span id="fontawesome-status">Loading...</span></li>
                <li>Custom CSS: <span id="custom-status">Loading...</span></li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-check"></i> Bootstrap Test</h5>
                    </div>
                    <div class="card-body">
                        <p>If you can see this card with proper styling, Bootstrap is working.</p>
                        <button class="btn btn-success">Success Button</button>
                        <button class="btn btn-warning">Warning Button</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5><i class="fas fa-palette"></i> Custom CSS Test</h5>
                    </div>
                    <div class="card-body">
                        <p>Testing custom styles from style.css</p>
                        <div class="hero-section p-3 rounded text-white">
                            <p>This should have gradient background if custom CSS loads</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Font Awesome Icons Test:</h3>
            <p>
                <i class="fas fa-home fa-2x text-primary"></i>
                <i class="fas fa-user fa-2x text-success"></i>
                <i class="fas fa-cog fa-2x text-warning"></i>
                <i class="fas fa-heart fa-2x text-danger"></i>
            </p>
        </div>
        
        <div class="mt-4">
            <h3>File Paths:</h3>
            <ul>
                <li>Bootstrap: <code>https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css</code></li>
                <li>Font Awesome: <code>https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css</code></li>
                <li>Custom CSS: <code>css/style.css</code></li>
            </ul>
        </div>
        
        <div class="mt-4">
            <h3>Direct Links:</h3>
            <ul>
                <li><a href="css/style.css" target="_blank">Test Custom CSS Direct Link</a></li>
                <li><a href="js/main.js" target="_blank">Test Custom JS Direct Link</a></li>
            </ul>
        </div>
    </div>

    <script>
        // Check if CSS files loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Check Bootstrap
            const testElement = document.createElement('div');
            testElement.className = 'container';
            document.body.appendChild(testElement);
            const computedStyle = window.getComputedStyle(testElement);
            
            if (computedStyle.maxWidth !== 'none') {
                document.getElementById('bootstrap-status').innerHTML = '<span class="text-success">✓ Loaded</span>';
            } else {
                document.getElementById('bootstrap-status').innerHTML = '<span class="text-danger">✗ Failed</span>';
            }
            
            document.body.removeChild(testElement);
            
            // Check Font Awesome
            const faTest = document.querySelector('.fas');
            if (faTest) {
                const faStyle = window.getComputedStyle(faTest, ':before');
                if (faStyle.fontFamily.includes('Font Awesome')) {
                    document.getElementById('fontawesome-status').innerHTML = '<span class="text-success">✓ Loaded</span>';
                } else {
                    document.getElementById('fontawesome-status').innerHTML = '<span class="text-danger">✗ Failed</span>';
                }
            }
            
            // Check Custom CSS
            const heroTest = document.querySelector('.hero-section');
            if (heroTest) {
                const heroStyle = window.getComputedStyle(heroTest);
                if (heroStyle.background.includes('gradient') || heroStyle.backgroundImage.includes('gradient')) {
                    document.getElementById('custom-status').innerHTML = '<span class="text-success">✓ Loaded</span>';
                } else {
                    document.getElementById('custom-status').innerHTML = '<span class="text-danger">✗ Failed</span>';
                }
            }
        });
    </script>
</body>
</html>
