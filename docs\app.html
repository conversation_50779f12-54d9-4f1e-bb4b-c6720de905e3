<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Pengaduan Warga - Live App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .app-container {
            min-height: 100vh;
            background: #f8f9fa;
        }
        .sidebar {
            background: #343a40;
            min-height: 100vh;
            width: 250px;
            position: fixed;
            left: -250px;
            transition: left 0.3s;
            z-index: 1000;
        }
        .sidebar.show {
            left: 0;
        }
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 12px 20px;
            border-radius: 0;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: #495057;
            color: white;
        }
        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s;
            padding: 20px;
        }
        .main-content.shifted {
            margin-left: 250px;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
        .status-pending { background-color: #fff3cd; color: #856404; }
        .status-process { background-color: #d1ecf1; color: #0c5460; }
        .status-completed { background-color: #d4edda; color: #155724; }
        .status-rejected { background-color: #f8d7da; color: #721c24; }
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .photo-preview {
            max-width: 100px;
            max-height: 80px;
            object-fit: cover;
            border-radius: 5px;
            cursor: pointer;
        }
        @media (max-width: 768px) {
            .main-content.shifted {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="text-center mb-4">
                <i class="fas fa-bullhorn fa-3x text-primary mb-3"></i>
                <h3>Sistem Pengaduan Warga</h3>
                <p class="text-muted">Silakan login untuk melanjutkan</p>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Role</label>
                <select class="form-select" id="loginRole">
                    <option value="user">Warga</option>
                    <option value="admin">Admin</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Email</label>
                <input type="email" class="form-control" id="loginEmail" placeholder="Masukkan email">
                <small class="text-muted">
                    Demo: user@demo.<NAME_EMAIL>
                </small>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Password</label>
                <input type="password" class="form-control" id="loginPassword" placeholder="Masukkan password">
                <small class="text-muted">
                    Demo: password123
                </small>
            </div>
            
            <button class="btn btn-primary w-100" onclick="login()">
                <i class="fas fa-sign-in-alt me-2"></i>Login
            </button>
            
            <div class="text-center mt-3">
                <small class="text-muted">
                    Belum punya akun? <a href="#" onclick="showRegister()">Daftar di sini</a>
                </small>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" style="display: none;">
        <!-- Top Navbar -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <button class="btn btn-outline-light me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <a class="navbar-brand" href="#">
                    <i class="fas fa-bullhorn me-2"></i>Sistem Pengaduan Warga
                </a>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><span id="currentUser">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showProfile()">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="p-3 border-bottom">
                <h6 class="text-light">Menu</h6>
            </div>
            <nav class="nav flex-column" id="sidebarNav">
                <!-- Menu items will be populated by JavaScript -->
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <div id="contentArea">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Photo Modal -->
    <div class="modal fade" id="photoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Foto Pengaduan</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="modalPhotoContent"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- GitHub Token Modal -->
    <div class="modal fade" id="githubTokenModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Setup GitHub Integration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Cara Setup:</h6>
                        <ol class="mb-0">
                            <li>Buka <a href="https://github.com/settings/tokens" target="_blank">GitHub Settings → Tokens</a></li>
                            <li>Klik "Generate new token (classic)"</li>
                            <li>Nama: "Pengaduan Warga App"</li>
                            <li>Scope: Pilih "public_repo"</li>
                            <li>Copy token dan paste di bawah</li>
                        </ol>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">GitHub Personal Access Token</label>
                        <input type="password" class="form-control" id="githubToken" placeholder="ghp_xxxxxxxxxxxx">
                        <small class="text-muted">Token akan disimpan di localStorage browser</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" onclick="saveGitHubToken()">Simpan Token</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="github-storage.js"></script>
    <script src="app.js"></script>
</body>
</html>
