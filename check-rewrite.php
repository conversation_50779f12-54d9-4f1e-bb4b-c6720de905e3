<?php
echo "<h1>Check mod_rewrite Status</h1>";

// Check if mod_rewrite is loaded
if (function_exists('apache_get_modules')) {
    $modules = apache_get_modules();
    if (in_array('mod_rewrite', $modules)) {
        echo "<p style='color: green;'>✓ mod_rewrite is ENABLED</p>";
    } else {
        echo "<p style='color: red;'>✗ mod_rewrite is DISABLED</p>";
    }
    
    echo "<h3>All Apache Modules:</h3>";
    echo "<ul>";
    foreach ($modules as $module) {
        echo "<li>$module</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: orange;'>⚠ Cannot detect Apache modules (function not available)</p>";
}

// Test .htaccess
echo "<h2>.htaccess Test</h2>";
if (file_exists('public/.htaccess')) {
    echo "<p style='color: green;'>✓ .htaccess file exists</p>";
    echo "<h3>Content:</h3>";
    echo "<pre>" . htmlspecialchars(file_get_contents('public/.htaccess')) . "</pre>";
} else {
    echo "<p style='color: red;'>✗ .htaccess file missing</p>";
}

// Test URL rewriting
echo "<h2>URL Rewrite Test</h2>";
echo "<p>Try these links:</p>";
echo "<ul>";
echo "<li><a href='public/auth/login'>Login (with rewrite)</a></li>";
echo "<li><a href='public/login.php'>Login (direct)</a></li>";
echo "<li><a href='public/auth/register'>Register (with rewrite)</a></li>";
echo "<li><a href='public/register.php'>Register (direct)</a></li>";
echo "</ul>";

echo "<h2>Instructions to Enable mod_rewrite:</h2>";
echo "<ol>";
echo "<li>Open XAMPP Control Panel</li>";
echo "<li>Click 'Config' next to Apache</li>";
echo "<li>Select 'Apache (httpd.conf)'</li>";
echo "<li>Find this line: <code>#LoadModule rewrite_module modules/mod_rewrite.so</code></li>";
echo "<li>Remove the # to uncomment it: <code>LoadModule rewrite_module modules/mod_rewrite.so</code></li>";
echo "<li>Find the Directory section for htdocs and change <code>AllowOverride None</code> to <code>AllowOverride All</code></li>";
echo "<li>Save the file and restart Apache</li>";
echo "</ol>";

echo "<h3>Example httpd.conf changes:</h3>";
echo "<pre>";
echo "# Uncomment this line:
LoadModule rewrite_module modules/mod_rewrite.so

# Change this section:
&lt;Directory \"C:/xampp/htdocs\"&gt;
    Options Indexes FollowSymLinks Includes ExecCGI
    AllowOverride All  &lt;-- Change from 'None' to 'All'
    Require all granted
&lt;/Directory&gt;";
echo "</pre>";
?>
