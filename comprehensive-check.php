<?php
/**
 * Comprehensive System Check
 */

require_once 'app/init.php';

echo "<h1>Comprehensive System Check</h1>";

// PHP Version and Extensions
echo "<h2>1. PHP Environment</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";

$requiredExtensions = ['pdo', 'pdo_mysql', 'gd', 'fileinfo', 'session', 'json'];
echo "<h3>Required Extensions:</h3>";
foreach ($requiredExtensions as $ext) {
    $status = extension_loaded($ext) ? "✓" : "✗";
    $color = extension_loaded($ext) ? "green" : "red";
    echo "<p style='color: $color;'>$status $ext</p>";
}

// Database Connection
echo "<h2>2. Database Connection</h2>";
try {
    $db = new Database();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test each model
    echo "<h3>Model Tests:</h3>";
    
    // Test User Model
    try {
        $userModel = new User();
        $users = $userModel->getUsers();
        echo "<p style='color: green;'>✓ User Model: " . count($users) . " users found</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ User Model Error: " . $e->getMessage() . "</p>";
    }
    
    // Test Category Model
    try {
        $categoryModel = new Category();
        $categories = $categoryModel->getCategories();
        echo "<p style='color: green;'>✓ Category Model: " . count($categories) . " categories found</p>";
        
        // Check for duplicates
        $names = array_column($categories, 'name');
        $duplicates = array_diff_assoc($names, array_unique($names));
        if (!empty($duplicates)) {
            echo "<p style='color: orange;'>⚠ Duplicate categories found: " . implode(', ', array_unique($duplicates)) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Category Model Error: " . $e->getMessage() . "</p>";
    }
    
    // Test Complaint Model
    try {
        $complaintModel = new Complaint();
        $complaints = $complaintModel->getComplaints();
        echo "<p style='color: green;'>✓ Complaint Model: " . count($complaints) . " complaints found</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Complaint Model Error: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// File Structure Check
echo "<h2>3. File Structure</h2>";
$requiredFiles = [
    'app/config/config.php',
    'app/config/database.php',
    'app/core/App.php',
    'app/core/Controller.php',
    'app/controllers/Home.php',
    'app/controllers/Auth.php',
    'app/controllers/Dashboard.php',
    'app/controllers/Admin.php',
    'app/controllers/Complaints.php',
    'app/models/User.php',
    'app/models/Category.php',
    'app/models/Complaint.php',
    'app/views/inc/header.php',
    'app/views/inc/footer.php',
    'public/index.php',
    'public/.htaccess'
];

foreach ($requiredFiles as $file) {
    $status = file_exists($file) ? "✓" : "✗";
    $color = file_exists($file) ? "green" : "red";
    echo "<p style='color: $color;'>$status $file</p>";
}

// Directory Permissions
echo "<h2>4. Directory Permissions</h2>";
$directories = [
    'public/uploads' => 'Upload directory',
    'logs' => 'Logs directory'
];

foreach ($directories as $dir => $desc) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? "✓ Writable" : "✗ Not writable";
        $color = is_writable($dir) ? "green" : "red";
        echo "<p style='color: $color;'>$writable - $desc</p>";
    } else {
        echo "<p style='color: red;'>✗ Missing - $desc</p>";
    }
}

// Configuration Check
echo "<h2>5. Configuration</h2>";
$configs = [
    'BASE_URL' => BASE_URL,
    'APP_NAME' => APP_NAME,
    'DEBUG' => DEBUG ? 'Enabled' : 'Disabled',
    'DB_HOST' => DB_HOST,
    'DB_NAME' => DB_NAME
];

foreach ($configs as $key => $value) {
    echo "<p><strong>$key:</strong> $value</p>";
}

// Security Check
echo "<h2>6. Security Check</h2>";

// Check for deprecated functions
echo "<h3>Deprecated Function Check:</h3>";
$controllers = ['Auth.php', 'Dashboard.php', 'Admin.php', 'Complaints.php'];
$hasDeprecated = false;

foreach ($controllers as $controller) {
    $file = "app/controllers/$controller";
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'FILTER_SANITIZE_STRING') !== false) {
            echo "<p style='color: red;'>✗ $controller still uses FILTER_SANITIZE_STRING</p>";
            $hasDeprecated = true;
        }
    }
}

if (!$hasDeprecated) {
    echo "<p style='color: green;'>✓ No deprecated FILTER_SANITIZE_STRING usage found</p>";
}

// Check error reporting
$errorLevel = error_reporting();
if (DEBUG) {
    echo "<p style='color: blue;'>ℹ Debug mode enabled - Error reporting: $errorLevel</p>";
} else {
    echo "<p style='color: green;'>✓ Production mode - Error reporting disabled</p>";
}

// URL Routing Test
echo "<h2>7. URL Routing Test</h2>";
echo "<h3>Test these URLs:</h3>";
echo "<ul>";
echo "<li><a href='" . BASE_URL . "' target='_blank'>Homepage</a></li>";
echo "<li><a href='" . BASE_URL . "index.php?url=auth/login' target='_blank'>Login</a></li>";
echo "<li><a href='" . BASE_URL . "index.php?url=auth/register' target='_blank'>Register</a></li>";
echo "</ul>";

// Validation Test
echo "<h2>8. Validation Functions Test</h2>";
$testEmail = '<EMAIL>';
$testPhone = '***********';
$testRequired = 'Test Value';

echo "<p>Email validation ($testEmail): " . (Validation::email($testEmail) ? "✓ Valid" : "✗ Invalid") . "</p>";
echo "<p>Phone validation ($testPhone): " . (Validation::phone($testPhone) ? "✓ Valid" : "✗ Invalid") . "</p>";
echo "<p>Required validation: " . (Validation::required($testRequired) === true ? "✓ Valid" : "✗ Invalid") . "</p>";

// Sanitization Test
echo "<h2>9. Sanitization Test</h2>";
$testData = '<script>alert("test")</script>Test Data';
$sanitized = sanitizeInput($testData);
echo "<p>Original: " . htmlspecialchars($testData) . "</p>";
echo "<p>Sanitized: " . htmlspecialchars($sanitized) . "</p>";

// Quick Actions
echo "<h2>10. Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='check-database.php'>Check Database & Clean Duplicates</a></li>";
echo "<li><a href='create-admin.php'>Create Admin Account</a></li>";
echo "<li><a href='test-php8-compatibility.php'>PHP 8 Compatibility Test</a></li>";
echo "<li><a href='" . BASE_URL . "'>Go to Application</a></li>";
echo "</ul>";

echo "<h2>Summary</h2>";
echo "<p>System check completed. Review any red ✗ items above and fix them if needed.</p>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
ul { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
