<?php
/**
 * Test Admin Login System
 */

require_once 'app/init.php';

echo "<h1>Test Admin Login System</h1>";

// Test 1: Check User Model
echo "<h2>1. Test User Model</h2>";
try {
    $userModel = new User();
    
    // Test getUsers method
    $users = $userModel->getUsers();
    echo "<p style='color: green;'>✓ User Model getUsers() works - Found " . count($users) . " users</p>";
    
    // Test emailExists method
    $emailExists = $userModel->emailExists('<EMAIL>');
    echo "<p style='color: green;'>✓ User Model emailExists() works - Admin email exists: " . ($emailExists ? 'Yes' : 'No') . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ User Model Error: " . $e->getMessage() . "</p>";
}

// Test 2: Check AdminAuth Controller
echo "<h2>2. Test AdminAuth Controller</h2>";
try {
    // Test PIN configuration
    $adminPin = defined('ADMIN_PIN') ? ADMIN_PIN : 'Not defined';
    echo "<p><strong>Admin PIN:</strong> $adminPin</p>";
    
    // Test session functions
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p style='color: green;'>✓ Session is active</p>";
    } else {
        echo "<p style='color: red;'>✗ Session is not active</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ AdminAuth Error: " . $e->getMessage() . "</p>";
}

// Test 3: Check Logger
echo "<h2>3. Test Logger</h2>";
try {
    // Test logging
    Logger::info('Test log entry from admin login test');
    Logger::security('Test security log');
    Logger::activity('Test activity log', 1, ['test' => true]);
    
    echo "<p style='color: green;'>✓ Logger works - Check logs/ directory</p>";
    
    // Show recent logs
    $recentLogs = Logger::getRecentLogs(5);
    if (!empty($recentLogs)) {
        echo "<h3>Recent Logs (last 5):</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
        foreach ($recentLogs as $log) {
            echo htmlspecialchars($log) . "\n";
        }
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Logger Error: " . $e->getMessage() . "</p>";
}

// Test 4: Check Helper Functions
echo "<h2>4. Test Helper Functions</h2>";
try {
    // Test getClientIP
    $clientIP = getClientIP();
    echo "<p><strong>Client IP:</strong> $clientIP</p>";
    
    // Test sanitization
    $testData = '<script>alert("test")</script>Test';
    $sanitized = sanitizeInput($testData);
    echo "<p><strong>Sanitization Test:</strong></p>";
    echo "<p>Original: " . htmlspecialchars($testData) . "</p>";
    echo "<p>Sanitized: " . htmlspecialchars($sanitized) . "</p>";
    
    echo "<p style='color: green;'>✓ Helper functions work</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Helper Functions Error: " . $e->getMessage() . "</p>";
}

// Test 5: File Structure
echo "<h2>5. File Structure Check</h2>";
$requiredFiles = [
    'app/controllers/AdminAuth.php',
    'app/views/adminauth/pin.php',
    'app/views/adminauth/login.php',
    'app/helpers/Logger.php',
    'public/images/hero-illustration.svg'
];

foreach ($requiredFiles as $file) {
    $status = file_exists($file) ? "✓" : "✗";
    $color = file_exists($file) ? "green" : "red";
    echo "<p style='color: $color;'>$status $file</p>";
}

// Test 6: Database Connection
echo "<h2>6. Database Connection</h2>";
try {
    $db = new Database();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Check admin user
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $stmt = $pdo->prepare("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin user found: " . $admin['email'] . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠ No admin user found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database Error: " . $e->getMessage() . "</p>";
}

// Test URLs
echo "<h2>7. Test URLs</h2>";
echo "<h3>Admin Login Flow:</h3>";
echo "<ol>";
echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/pin' target='_blank'>Step 1: Admin PIN Entry</a></li>";
echo "<li>Enter PIN: <strong>$adminPin</strong></li>";
echo "<li>Step 2: Admin Login (after PIN verification)</li>";
echo "<li>Login with admin credentials</li>";
echo "<li><a href='" . BASE_URL . "index.php?url=admin' target='_blank'>Step 3: Admin Dashboard</a></li>";
echo "<li><a href='" . BASE_URL . "index.php?url=adminauth/logout' target='_blank'>Step 4: Admin Logout</a></li>";
echo "</ol>";

echo "<h3>Regular User URLs:</h3>";
echo "<ul>";
echo "<li><a href='" . BASE_URL . "' target='_blank'>Homepage</a></li>";
echo "<li><a href='" . BASE_URL . "index.php?url=auth/login' target='_blank'>User Login</a></li>";
echo "<li><a href='" . BASE_URL . "index.php?url=auth/register' target='_blank'>User Register</a></li>";
echo "</ul>";

// Summary
echo "<h2>8. Summary</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #2196F3;'>";
echo "<h3>Admin Login System Features:</h3>";
echo "<ul>";
echo "<li>✅ <strong>PIN Protection:</strong> Hardcoded PIN ($adminPin) required before login</li>";
echo "<li>✅ <strong>Session Management:</strong> PIN valid for 1 hour</li>";
echo "<li>✅ <strong>Security Logging:</strong> All admin activities logged</li>";
echo "<li>✅ <strong>Separate Logout:</strong> Admin logout clears all sessions</li>";
echo "<li>✅ <strong>Error Handling:</strong> Proper error messages and validation</li>";
echo "<li>✅ <strong>UI/UX:</strong> Modern, responsive design</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-top: 15px;'>";
echo "<h3>Security Notes:</h3>";
echo "<ul>";
echo "<li>🔐 PIN is hardcoded in config for maximum security</li>";
echo "<li>📝 All admin access attempts are logged</li>";
echo "<li>⏰ PIN verification expires after 1 hour</li>";
echo "<li>🚫 Failed PIN attempts are tracked</li>";
echo "<li>🔄 Admin logout clears all session data</li>";
echo "</ul>";
echo "</div>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
