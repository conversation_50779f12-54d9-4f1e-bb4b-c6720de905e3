<?php
/**
 * Check Database for Duplicates and Issues
 */

require_once 'app/config/config.php';

echo "<h1>Database Check & Cleanup</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Check categories
echo "<h2>Categories Check</h2>";

// Get all categories
$stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Current Categories (" . count($categories) . " total):</h3>";
if (count($categories) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Name</th><th>Description</th><th>Created</th><th>Action</th></tr>";
    
    $nameCount = [];
    foreach ($categories as $cat) {
        $name = $cat['name'];
        $nameCount[$name] = ($nameCount[$name] ?? 0) + 1;
        
        $isDuplicate = $nameCount[$name] > 1;
        $rowStyle = $isDuplicate ? "background: #ffebee;" : "";
        
        echo "<tr style='$rowStyle'>";
        echo "<td>" . $cat['id'] . "</td>";
        echo "<td>" . htmlspecialchars($cat['name']) . ($isDuplicate ? " <span style='color: red;'>(DUPLICATE)</span>" : "") . "</td>";
        echo "<td>" . htmlspecialchars($cat['description']) . "</td>";
        echo "<td>" . date('d/m/Y', strtotime($cat['created_at'])) . "</td>";
        echo "<td>";
        if ($isDuplicate) {
            echo "<a href='?delete_category=" . $cat['id'] . "' onclick='return confirm(\"Delete this duplicate?\")' style='color: red;'>Delete</a>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show duplicates summary
    $duplicates = array_filter($nameCount, function($count) { return $count > 1; });
    if (!empty($duplicates)) {
        echo "<h3 style='color: red;'>Duplicate Categories Found:</h3>";
        echo "<ul>";
        foreach ($duplicates as $name => $count) {
            echo "<li><strong>$name</strong> appears $count times</li>";
        }
        echo "</ul>";
        echo "<p><a href='?cleanup_categories=1' onclick='return confirm(\"This will remove duplicate categories. Continue?\")' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Clean Up Duplicates</a></p>";
    } else {
        echo "<p style='color: green;'>✓ No duplicate categories found</p>";
    }
} else {
    echo "<p>No categories found. <a href='?add_default_categories=1'>Add Default Categories</a></p>";
}

// Handle cleanup
if (isset($_GET['cleanup_categories'])) {
    echo "<h3>Cleaning up duplicate categories...</h3>";
    
    // Keep only the first occurrence of each category name
    $stmt = $pdo->prepare("
        DELETE c1 FROM categories c1
        INNER JOIN categories c2 
        WHERE c1.id > c2.id AND c1.name = c2.name
    ");
    
    if ($stmt->execute()) {
        $deleted = $stmt->rowCount();
        echo "<p style='color: green;'>✓ Removed $deleted duplicate categories</p>";
        echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
    } else {
        echo "<p style='color: red;'>✗ Failed to clean up duplicates</p>";
    }
}

// Handle single delete
if (isset($_GET['delete_category'])) {
    $id = (int)$_GET['delete_category'];
    $stmt = $pdo->prepare("DELETE FROM categories WHERE id = ?");
    if ($stmt->execute([$id])) {
        echo "<p style='color: green;'>✓ Category deleted</p>";
        echo "<script>setTimeout(function(){ location.href = '?'; }, 1000);</script>";
    }
}

// Handle add default categories
if (isset($_GET['add_default_categories'])) {
    echo "<h3>Adding default categories...</h3>";
    
    $defaultCategories = [
        ['Infrastruktur Jalan', 'Keluhan terkait jalan rusak, lubang, penerangan jalan'],
        ['Kebersihan Lingkungan', 'Masalah sampah, drainase, kebersihan fasilitas umum'],
        ['Keamanan dan Ketertiban', 'Gangguan keamanan, ketertiban umum, premanisme'],
        ['Pelayanan Publik', 'Keluhan pelayanan di kantor pemerintahan, rumah sakit, sekolah'],
        ['Fasilitas Umum', 'Kerusakan taman, toilet umum, halte, fasilitas olahraga'],
        ['Lingkungan Hidup', 'Pencemaran air, udara, kebisingan, penebangan liar'],
        ['Transportasi', 'Masalah angkutan umum, parkir, kemacetan'],
        ['Kesehatan', 'Layanan kesehatan, puskesmas, rumah sakit'],
        ['Pendidikan', 'Fasilitas sekolah, tenaga pengajar, sarana belajar'],
        ['Lainnya', 'Pengaduan yang tidak termasuk kategori di atas']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
    $added = 0;
    
    foreach ($defaultCategories as $cat) {
        // Check if category already exists
        $checkStmt = $pdo->prepare("SELECT COUNT(*) FROM categories WHERE name = ?");
        $checkStmt->execute([$cat[0]]);
        
        if ($checkStmt->fetchColumn() == 0) {
            if ($stmt->execute($cat)) {
                $added++;
                echo "<p style='color: green;'>✓ Added: " . $cat[0] . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Already exists: " . $cat[0] . "</p>";
        }
    }
    
    echo "<p><strong>Added $added new categories</strong></p>";
    echo "<script>setTimeout(function(){ location.href = '?'; }, 3000);</script>";
}

// Check users
echo "<h2>Users Check</h2>";
$stmt = $pdo->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
$userStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'><th>Role</th><th>Count</th></tr>";
foreach ($userStats as $stat) {
    echo "<tr><td>" . ucfirst($stat['role']) . "</td><td>" . $stat['count'] . "</td></tr>";
}
echo "</table>";

// Check complaints
echo "<h2>Complaints Check</h2>";
$stmt = $pdo->query("SELECT status, COUNT(*) as count FROM complaints GROUP BY status");
$complaintStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($complaintStats) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'><th>Status</th><th>Count</th></tr>";
    foreach ($complaintStats as $stat) {
        echo "<tr><td>" . ucfirst($stat['status']) . "</td><td>" . $stat['count'] . "</td></tr>";
    }
    echo "</table>";
} else {
    echo "<p>No complaints found yet.</p>";
}

// Check for orphaned data
echo "<h2>Data Integrity Check</h2>";

// Check complaints without valid categories
$stmt = $pdo->query("
    SELECT COUNT(*) as count 
    FROM complaints c 
    LEFT JOIN categories cat ON c.category_id = cat.id 
    WHERE cat.id IS NULL
");
$orphanedComplaints = $stmt->fetchColumn();

if ($orphanedComplaints > 0) {
    echo "<p style='color: red;'>⚠ Found $orphanedComplaints complaints with invalid category references</p>";
} else {
    echo "<p style='color: green;'>✓ All complaints have valid category references</p>";
}

// Check complaints without valid users
$stmt = $pdo->query("
    SELECT COUNT(*) as count 
    FROM complaints c 
    LEFT JOIN users u ON c.user_id = u.id 
    WHERE u.id IS NULL
");
$orphanedUserComplaints = $stmt->fetchColumn();

if ($orphanedUserComplaints > 0) {
    echo "<p style='color: red;'>⚠ Found $orphanedUserComplaints complaints with invalid user references</p>";
} else {
    echo "<p style='color: green;'>✓ All complaints have valid user references</p>";
}

echo "<h2>Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='?add_default_categories=1'>Add Default Categories</a></li>";
echo "<li><a href='?cleanup_categories=1'>Clean Up Duplicate Categories</a></li>";
echo "<li><a href='" . BASE_URL . "'>Go to Application</a></li>";
echo "<li><a href='create-admin.php'>Create Admin Account</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
