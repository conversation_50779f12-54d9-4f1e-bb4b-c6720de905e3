<?php
class Dashboard extends Controller {
    public function __construct() {
        if (!$this->isLoggedIn()) {
            $this->redirect('auth/login');
        }
        
        $this->complaintModel = $this->model('Complaint');
        $this->userModel = $this->model('User');
    }
    
    public function index() {
        // Get user's complaints
        $complaints = $this->complaintModel->getComplaintsByUser($_SESSION['user_id']);
        
        // Get user statistics
        $stats = [
            'total' => count($complaints),
            'pending' => count(array_filter($complaints, function($c) { return $c->status == 'pending'; })),
            'process' => count(array_filter($complaints, function($c) { return $c->status == 'process'; })),
            'completed' => count(array_filter($complaints, function($c) { return $c->status == 'completed'; })),
            'rejected' => count(array_filter($complaints, function($c) { return $c->status == 'rejected'; }))
        ];
        
        $data = [
            'title' => 'Dashboard - ' . APP_NAME,
            'complaints' => $complaints,
            'stats' => $stats
        ];
        
        $this->view('dashboard/index', $data);
    }
    
    public function profile() {
        $user = $this->userModel->getUserById($_SESSION['user_id']);
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            $data = [
                'id' => $_SESSION['user_id'],
                'name' => trim($_POST['name']),
                'phone' => trim($_POST['phone']),
                'address' => trim($_POST['address']),
                'name_err' => '',
                'phone_err' => '',
                'address_err' => ''
            ];
            
            // Validate Name
            if (empty($data['name'])) {
                $data['name_err'] = 'Silakan masukkan nama';
            }
            
            // Make sure errors are empty
            if (empty($data['name_err']) && empty($data['phone_err']) && empty($data['address_err'])) {
                // Update profile
                if ($this->userModel->updateProfile($data)) {
                    // Update session
                    $_SESSION['user_name'] = $data['name'];
                    $this->flash('profile_message', 'Profil berhasil diperbarui');
                    $this->redirect('dashboard/profile');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $data['user'] = $user;
                $this->view('dashboard/profile', $data);
            }
        } else {
            $data = [
                'title' => 'Profil - ' . APP_NAME,
                'user' => $user,
                'name_err' => '',
                'phone_err' => '',
                'address_err' => ''
            ];
            
            $this->view('dashboard/profile', $data);
        }
    }
}
?>
