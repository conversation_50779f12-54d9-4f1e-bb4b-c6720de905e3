<?php
/**
 * Check Database Structure
 */

require_once 'app/config/config.php';

echo "<h1>Check Database Structure</h1>";

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} catch (PDOException $e) {
    die("<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>");
}

// Check users table structure
echo "<h2>Users Table Structure</h2>";
$stmt = $pdo->query("DESCRIBE users");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>" . $column['Field'] . "</td>";
    echo "<td>" . $column['Type'] . "</td>";
    echo "<td>" . $column['Null'] . "</td>";
    echo "<td>" . $column['Key'] . "</td>";
    echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
    echo "<td>" . $column['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check current users
echo "<h2>Current Users</h2>";
$stmt = $pdo->query("SELECT id, name, email, role, created_at FROM users");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (count($users) > 0) {
    echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Created</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . date('d/m/Y H:i', strtotime($user['created_at'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No users found</p>";
}

// Check if role column is ENUM
echo "<h2>Role Column Analysis</h2>";
$stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'role'");
$roleColumn = $stmt->fetch(PDO::FETCH_ASSOC);

if ($roleColumn) {
    echo "<p><strong>Role Column Type:</strong> " . $roleColumn['Type'] . "</p>";
    
    if (strpos($roleColumn['Type'], 'enum') !== false) {
        echo "<p style='color: orange;'>⚠ Role column is ENUM type</p>";
        
        // Extract ENUM values
        preg_match_all("/'([^']+)'/", $roleColumn['Type'], $matches);
        $enumValues = $matches[1];
        
        echo "<p><strong>Allowed values:</strong> " . implode(', ', $enumValues) . "</p>";
        
        if (!in_array('user', $enumValues)) {
            echo "<p style='color: red;'>✗ 'user' is not in allowed ENUM values</p>";
            echo "<p><a href='?fix_role_enum=1'>Fix Role ENUM Values</a></p>";
        } else {
            echo "<p style='color: green;'>✓ 'user' is allowed in ENUM values</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Role column is not ENUM (flexible)</p>";
    }
}

// Handle fix role enum
if (isset($_GET['fix_role_enum'])) {
    echo "<h3>Fixing Role ENUM Values...</h3>";
    
    try {
        // Modify the role column to include 'user' and 'admin'
        $pdo->exec("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'user') NOT NULL DEFAULT 'user'");
        echo "<p style='color: green;'>✓ Role ENUM values updated to include 'admin' and 'user'</p>";
        echo "<script>setTimeout(function(){ location.href = '?'; }, 2000);</script>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Failed to update role ENUM: " . $e->getMessage() . "</p>";
    }
}

// Check complaints table structure
echo "<h2>Complaints Table Structure</h2>";
$stmt = $pdo->query("DESCRIBE complaints");
$complaintColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($complaintColumns as $column) {
    echo "<tr>";
    echo "<td>" . $column['Field'] . "</td>";
    echo "<td>" . $column['Type'] . "</td>";
    echo "<td>" . $column['Null'] . "</td>";
    echo "<td>" . $column['Key'] . "</td>";
    echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
    echo "<td>" . $column['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check categories table
echo "<h2>Categories Table Structure</h2>";
$stmt = $pdo->query("DESCRIBE categories");
$categoryColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background: #f8f9fa;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($categoryColumns as $column) {
    echo "<tr>";
    echo "<td>" . $column['Field'] . "</td>";
    echo "<td>" . $column['Type'] . "</td>";
    echo "<td>" . $column['Null'] . "</td>";
    echo "<td>" . $column['Key'] . "</td>";
    echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
    echo "<td>" . $column['Extra'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test insert with correct role values
echo "<h2>Test Role Values</h2>";
if ($roleColumn && strpos($roleColumn['Type'], 'enum') !== false) {
    preg_match_all("/'([^']+)'/", $roleColumn['Type'], $matches);
    $enumValues = $matches[1];
    
    echo "<p>Testing role values:</p>";
    foreach ($enumValues as $value) {
        echo "<p>✓ '$value' - Valid ENUM value</p>";
    }
    
    if (!in_array('user', $enumValues)) {
        echo "<p style='color: red;'>✗ 'user' is not valid. Use one of: " . implode(', ', $enumValues) . "</p>";
    }
}

echo "<h2>Recommendations</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h3>To Fix the Role Issue:</h3>";
echo "<ol>";
echo "<li>Check if role column is ENUM type</li>";
echo "<li>If ENUM, make sure 'user' and 'admin' are included</li>";
echo "<li>If not, modify the ENUM values or change to VARCHAR</li>";
echo "<li>Update create-sample-data.php to use correct role values</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Quick Actions</h2>";
echo "<ul>";
echo "<li><a href='?fix_role_enum=1'>Fix Role ENUM Values</a></li>";
echo "<li><a href='create-sample-data.php'>Try Create Sample Data Again</a></li>";
echo "<li><a href='fix-admin-account.php'>Fix Admin Account</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
